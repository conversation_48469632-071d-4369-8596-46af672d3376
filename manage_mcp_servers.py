#!/usr/bin/env python3
"""
MCP服务器管理脚本
用于禁用/启用或删除MCP服务器配置
"""

import json
import os
import shutil
from pathlib import Path

def find_mcp_configs():
    """查找所有MCP配置文件"""
    configs = []
    
    # 常见的MCP配置文件位置
    possible_paths = [
        Path.home() / ".cursor" / "mcp.json",
        Path.home() / ".vscode" / "mcp.json", 
        Path.home() / ".lingma" / "extension" / "local" / "mcp.json",
        Path.home() / "Library" / "Application Support" / "Trae" / "User" / "mcp.json",
        Path.cwd() / ".cursor" / "mcp.json",
        Path.cwd() / ".vscode" / "mcp.json",
        Path.cwd() / ".kilocode" / "mcp.json",
        Path.cwd() / ".roo" / "mcp.json"
    ]
    
    for path in possible_paths:
        if path.exists():
            configs.append(path)
    
    return configs

def backup_config(config_path):
    """备份配置文件"""
    backup_path = config_path.with_suffix('.json.backup')
    shutil.copy2(config_path, backup_path)
    print(f"✅ 已备份: {backup_path}")

def disable_all_servers(config_path):
    """禁用配置文件中的所有MCP服务器"""
    try:
        with open(config_path, 'r', encoding='utf-8') as f:
            config = json.load(f)
        
        if 'mcpServers' in config:
            for server_name, server_config in config['mcpServers'].items():
                server_config['disabled'] = True
                print(f"  - 禁用服务器: {server_name}")
        
        with open(config_path, 'w', encoding='utf-8') as f:
            json.dump(config, f, indent=2, ensure_ascii=False)
        
        print(f"✅ 已禁用 {config_path} 中的所有服务器")
        
    except Exception as e:
        print(f"❌ 处理 {config_path} 时出错: {e}")

def enable_all_servers(config_path):
    """启用配置文件中的所有MCP服务器"""
    try:
        with open(config_path, 'r', encoding='utf-8') as f:
            config = json.load(f)
        
        if 'mcpServers' in config:
            for server_name, server_config in config['mcpServers'].items():
                if 'disabled' in server_config:
                    del server_config['disabled']
                print(f"  - 启用服务器: {server_name}")
        
        with open(config_path, 'w', encoding='utf-8') as f:
            json.dump(config, f, indent=2, ensure_ascii=False)
        
        print(f"✅ 已启用 {config_path} 中的所有服务器")
        
    except Exception as e:
        print(f"❌ 处理 {config_path} 时出错: {e}")

def remove_config(config_path):
    """删除配置文件"""
    try:
        backup_config(config_path)
        os.remove(config_path)
        print(f"✅ 已删除配置文件: {config_path}")
    except Exception as e:
        print(f"❌ 删除 {config_path} 时出错: {e}")

def show_config_info(config_path):
    """显示配置文件信息"""
    try:
        with open(config_path, 'r', encoding='utf-8') as f:
            config = json.load(f)
        
        print(f"\n📁 配置文件: {config_path}")
        if 'mcpServers' in config:
            for server_name, server_config in config['mcpServers'].items():
                status = "🔴 禁用" if server_config.get('disabled', False) else "🟢 启用"
                print(f"  - {server_name}: {status}")
        else:
            print("  - 无MCP服务器配置")
            
    except Exception as e:
        print(f"❌ 读取 {config_path} 时出错: {e}")

def main():
    print("🔍 查找MCP配置文件...")
    configs = find_mcp_configs()
    
    if not configs:
        print("❌ 未找到MCP配置文件")
        return
    
    print(f"✅ 找到 {len(configs)} 个配置文件")
    
    # 显示所有配置信息
    for config in configs:
        show_config_info(config)
    
    print("\n请选择操作:")
    print("1. 禁用所有MCP服务器")
    print("2. 启用所有MCP服务器") 
    print("3. 删除所有MCP配置文件")
    print("4. 仅显示信息")
    print("0. 退出")
    
    choice = input("\n请输入选择 (0-4): ").strip()
    
    if choice == "1":
        print("\n🔄 禁用所有MCP服务器...")
        for config in configs:
            backup_config(config)
            disable_all_servers(config)
    
    elif choice == "2":
        print("\n🔄 启用所有MCP服务器...")
        for config in configs:
            backup_config(config)
            enable_all_servers(config)
    
    elif choice == "3":
        confirm = input("\n⚠️  确定要删除所有MCP配置文件吗? (y/N): ").strip().lower()
        if confirm == 'y':
            print("\n🗑️  删除所有MCP配置文件...")
            for config in configs:
                remove_config(config)
        else:
            print("❌ 操作已取消")
    
    elif choice == "4":
        print("\n✅ 信息已显示")
    
    elif choice == "0":
        print("👋 再见!")
    
    else:
        print("❌ 无效选择")

if __name__ == "__main__":
    main()
