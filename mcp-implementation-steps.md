# MCP文件系统服务器实施步骤

## 1. MCP设置文件配置

需要更新位于以下位置的`mcp_settings.json`文件：
```
/Users/<USER>/Library/Application Support/Code/User/globalStorage/kilocode.kilo-code/settings/mcp_settings.json
```

将其内容更新为：

```json
{
  "mcpServers": {
    "github.com/modelcontextprotocol/servers/tree/main/src/filesystem": {
      "command": "npx",
      "args": [
        "-y",
        "@modelcontextprotocol/server-filesystem",
        "/Users/<USER>/Downloads/Test3D"
      ]
    }
  }
}
```

## 2. 创建服务器目录

创建一个目录来存放MCP服务器相关文件：

```bash
mkdir -p /Users/<USER>/Downloads/Test3D/mcp-servers/filesystem
```

## 3. 实施步骤

1. 切换到Code模式
2. 更新`mcp_settings.json`文件
3. 创建MCP服务器目录
4. 重启VS Code或刷新MCP服务器
5. 测试MCP服务器功能

## 4. 测试命令

以下是用于测试MCP服务器功能的示例代码：

```javascript
// 使用list_directory工具列出目录内容
<use_mcp_tool>
<server_name>github.com/modelcontextprotocol/servers/tree/main/src/filesystem</server_name>
<tool_name>list_directory</tool_name>
<arguments>
{
  "path": "/Users/<USER>/Downloads/Test3D"
}
</arguments>
</use_mcp_tool>

// 使用read_file工具读取文件内容
<use_mcp_tool>
<server_name>github.com/modelcontextprotocol/servers/tree/main/src/filesystem</server_name>
<tool_name>read_file</tool_name>
<arguments>
{
  "path": "/Users/<USER>/Downloads/Test3D/Test3D/AppDelegate.swift"
}
</arguments>
</use_mcp_tool>

// 使用search_files工具搜索文件
<use_mcp_tool>
<server_name>github.com/modelcontextprotocol/servers/tree/main/src/filesystem</server_name>
<tool_name>search_files</tool_name>
<arguments>
{
  "path": "/Users/<USER>/Downloads/Test3D",
  "pattern": "*.swift"
}
</arguments>
</use_mcp_tool>
```

## 5. 故障排除

如果遇到问题，请检查：

1. Node.js和NPX是否正确安装：
```bash
node --version && npx --version
```

2. 配置文件格式是否正确
3. 权限问题 - 确保有足够的权限访问指定目录
4. 重启VS Code后再次测试