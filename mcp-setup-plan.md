# 文件系统MCP服务器设置计划

## 1. 环境分析

当前环境：
- 操作系统：macOS Sequoia
- 默认Shell：/bin/zsh
- 工作目录：/Users/<USER>/Downloads/Test3D
- 项目类型：Swift/Metal 3D渲染应用

## 2. 设置流程图

```mermaid
flowchart TD
    A[确认Node.js和NPX环境] --> B[创建MCP服务器目录]
    B --> C[更新mcp_settings.json配置]
    C --> D[重启VS Code或刷新MCP服务器]
    D --> E[测试服务器功能]
    E --> F1[列出目录内容]
    E --> F2[读取文件内容]
    E --> F3[搜索文件]
```

## 3. 详细步骤

### 3.1 创建MCP服务器目录

在当前工作目录中创建一个新目录用于MCP服务器：

```bash
mkdir -p /Users/<USER>/Downloads/Test3D/mcp-servers/filesystem
```

### 3.2 更新mcp_settings.json配置

需要更新位于`/Users/<USER>/Library/Application Support/Code/User/globalStorage/kilocode.kilo-code/settings/mcp_settings.json`的配置文件。

配置内容如下：

```json
{
  "mcpServers": {
    "github.com/modelcontextprotocol/servers/tree/main/src/filesystem": {
      "command": "npx",
      "args": [
        "-y",
        "@modelcontextprotocol/server-filesystem",
        "/Users/<USER>/Downloads/Test3D"
      ]
    }
  }
}
```

### 3.3 重启VS Code或刷新MCP服务器

配置更新后，需要重启VS Code或刷新MCP服务器使配置生效。

### 3.4 测试服务器功能

使用服务器提供的工具进行测试：

1. **列出目录内容**
   - 使用`list_directory`工具列出当前目录内容

2. **读取文件内容**
   - 使用`read_file`工具读取项目中的文件，如`Test3D/AppDelegate.swift`

3. **搜索文件**
   - 使用`search_files`工具搜索项目中包含特定关键词的文件

## 4. 测试用例

以下是几个测试用例，用于验证MCP服务器功能：

1. **列出目录内容**：
```json
{
  "server_name": "github.com/modelcontextprotocol/servers/tree/main/src/filesystem",
  "tool_name": "list_directory",
  "arguments": {
    "path": "/Users/<USER>/Downloads/Test3D"
  }
}
```

2. **读取文件内容**：
```json
{
  "server_name": "github.com/modelcontextprotocol/servers/tree/main/src/filesystem",
  "tool_name": "read_file",
  "arguments": {
    "path": "/Users/<USER>/Downloads/Test3D/Test3D/AppDelegate.swift"
  }
}
```

3. **搜索文件**：
```json
{
  "server_name": "github.com/modelcontextprotocol/servers/tree/main/src/filesystem",
  "tool_name": "search_files",
  "arguments": {
    "path": "/Users/<USER>/Downloads/Test3D",
    "pattern": "*.swift"
  }
}
```

## 5. 注意事项

1. 选择NPX方式安装的原因：
   - 在macOS上更简单易用
   - 不需要处理Docker的挂载问题
   - 不需要安装额外的Docker软件

2. 服务器只允许在指定目录内进行操作，配置中我们指定了当前工作目录。

3. 确保Node.js和NPX已正确安装，可通过以下命令验证：
   ```bash
   node --version && npx --version
   ```

4. 如遇到权限问题，可能需要使用sudo或调整目录权限。